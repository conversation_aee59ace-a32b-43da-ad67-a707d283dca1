const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: () => Date.now() },
  user: { type: String, ref: 'User' },
  imageKey: { type: String },
  imageUrl: { type: String },
  status: { type: String },
  rejectionReason: { type: String },
  processingTime: { type: Number },
  yotiRequestId: { type: String },
  isError: { type: Boolean, default: undefined },
  error: {
    errorMessage: { type: String },
    errorCode: { type: String },
    errorStatusCode: { type: Number },
  },
  isMultiframe: { type: Boolean, default: undefined },
});

schema.index({
  user: 1,
  createdAt: 1,
});

// Export schema =====================================================================================================================================================================
let conn = connectionLib.getRecordsConnection() || mongoose;
module.exports = conn.model('YotiVerification', schema);
