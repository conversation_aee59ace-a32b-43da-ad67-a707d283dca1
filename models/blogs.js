const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema(
    {
        images: [{ type: String }],
        pollData: { type: mongoose.Mixed },
        /* expected datatype for pollData
        pollData: { 
        0 : { INTF: 5, ISTJ: 3 ...... }, 
        1 : { INTF: 2, ISTJ: 11 ...... }, 
        }
        */
    }, 
    { strict: false }
);

let conn = connectionLib.getBlogsConnection() || mongoose;
module.exports = conn.model('blogs', schema);
