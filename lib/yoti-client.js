const { RequestBuilder, Payload } = require('yoti');
const { cloudwatch } = require('./cloudwatch');
const { YOTI_URL, YOTI_PEM_PATH, YOTI_LIVENESS_ENDPOINT, YOTI_SDK_ID } = require('./constants');

const getYotiDetectionResult = async (payload) => {
  const startTime = Date.now();
  let result = {};
  try {
    const secure = payload?.secure;
    const request = new RequestBuilder()
      .withBaseUrl(YOTI_URL)
      .withPemFilePath(YOTI_PEM_PATH)
      .withEndpoint(YOTI_LIVENESS_ENDPOINT)
      .withPayload(new Payload(payload))
      .withMethod('POST')
      .withHeader('X-Yoti-Auth-Id', YOTI_SDK_ID)
      .withQueryParam('secure', !!secure)
      .withQueryParam('multiframe', true)
      .build();
    const response = await request.execute();
    result = response.parsedResponse;
    result.yotiRequestId = response.headers?.['x-request-id'] || '';
  } catch (error) {
    console.log(`ThirdPartyError Yoti:`, error.message);
    result.isError = true;
    result.yotiRequestId = error.response?.headers?.['x-request-id'] || '';
    if (error.response?.text) {
      try {
        const { error_message, error_code } = JSON.parse(error.response.text);
        result.error = {
          errorMessage: error_message,
          errorCode: error_code,
          errorStatusCode: error.status || 500,
        };
      } catch (e) {
        console.log(`Yoti error parsing failed with error: ${e.message}, Yoit error: ${error}`);
        result.error = {
          errorMessage: error.message,
          errorCode: 'ERROR_STRUCTURE_MISMATCHED_WITH_YOTI',
          errorStatusCode: 502,
        };
      }
    }
    await reportErrorToCloudWatch('Yoti');
  }
  result.processingTime = Date.now() - startTime;
  return result;
};

const reportErrorToCloudWatch = async (serviceName) => {
  const params = {
    MetricData: [
      {
        MetricName: `${serviceName}Errors`,
        Value: 1,
      },
    ],
    Namespace: `ThirdPartyMetrics_${process.env.NODE_ENV}`,
  };
  await cloudwatch.putMetricData(params).promise();
};

module.exports = {
  getYotiDetectionResult,
};
