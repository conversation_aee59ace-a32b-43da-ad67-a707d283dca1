/*
ENV Required:
<PERSON><PERSON><PERSON> willl work for these two
YOTI_URL,
YOTI_LIVENESS_ENDPOINT,

YOTI_PEM_PATH,
YOTI_SDK_ID
*/
const { getYotiDetectionResult } = require('../lib/yoti-client');
const fs = require('fs').promises;

const testYotiDetection = async () => {
  const inputFile = `${__dirname}/test-yoti-payload.json`;
  const fileContent = await fs.readFile(inputFile, 'utf8');
  const payload = JSON.parse(fileContent);
  const result = await getYotiDetectionResult(payload, true);
  console.log('Got response from Yoti:', result);
};

testYotiDetection();
