{"name": "backend", "version": "1.0.0", "description": "", "main": "app.js", "engines": {"node": "^16.x", "npm": "^8.x"}, "scripts": {"start": "cross-env-shell \"$NODE_COMMAND\"", "test": "env NODE_ENV=test TESTING=1 NO_RESTRICT_SIGNUP=1 PAGE_SIZE=10 DAILY_PROFILE_LIMIT=4 DAILY_LIKES_LIMIT=2 REPORTS_UNTIL_DELETION=2 SPAM_THRESHOLD=1 COOLDOWN=500 INSTANT_MATCH_DELAY_MS=1 KARMA_SQRT_THRESHOLD=1 CANDIDATE_MAX_COUNT=2 TRANSLATION_MAX_COUNT=2 OPENAI_KEY=test REPLICATE_KEY=test GROQ_API_KEY=test IMGPLN_SERVER_ID=abcd1234 mocha --timeout 100000 --exclude 'test/user-rate-limiter.js'", "testc": "env NODE_ENV=test TESTING=1 NO_RESTRICT_SIGNUP=1 PAGE_SIZE=10 DAILY_PROFILE_LIMIT=4 DAILY_LIKES_LIMIT=2 REPORTS_UNTIL_DELETION=2 SPAM_THRESHOLD=1 COOLDOWN=500 INSTANT_MATCH_DELAY_MS=1 KARMA_SQRT_THRESHOLD=1 CANDIDATE_MAX_COUNT=2 TRANSLATION_MAX_COUNT=2 OPENAI_KEY=test REPLICATE_KEY=test GROQ_API_KEY=test IMGPLN_SERVER_ID=abcd1234 nyc mocha --timeout 100000 --exclude 'test/user-rate-limiter.js'", "test:user-rate-limiter": "env NODE_ENV=test_limiter TESTING=1 NO_RESTRICT_SIGNUP=1 PAGE_SIZE=10 DAILY_PROFILE_LIMIT=4 DAILY_LIKES_LIMIT=2 REPORTS_UNTIL_DELETION=2 SPAM_THRESHOLD=1 COOLDOWN=500 INSTANT_MATCH_DELAY_MS=1 KARMA_SQRT_THRESHOLD=1 CANDIDATE_MAX_COUNT=2 TRANSLATION_MAX_COUNT=2 OPENAI_KEY=test REPLICATE_KEY=test GROQ_API_KEY=test IMGPLN_SERVER_ID=abcd1234 mocha test/user-rate-limiter.js --timeout 100000", "eslint": "npx eslint . --ext .js --fix"}, "repository": {"type": "git", "url": "git+ssh://**************/get.china/backend.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://gitlab.com/get.china/backend/issues"}, "homepage": "https://gitlab.com/get.china/backend#readme", "dependencies": {"@anthropic-ai/sdk": "^0.18.0", "@aws-sdk/client-sqs": "^3.782.0", "@deepgram/sdk": "^3.6.0", "@google-cloud/translate": "^7.0.5", "@google-cloud/vision": "^3.0.0", "@mailchimp/mailchimp_marketing": "^3.0.37", "@mailchimp/mailchimp_transactional": "^1.0.46", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.47.1", "@opentelemetry/exporter-trace-otlp-proto": "^0.52.1", "@opentelemetry/instrumentation": "^0.52.1", "@opentelemetry/instrumentation-mongodb": "^0.53.0", "@opentelemetry/instrumentation-mongoose": "^0.39.0", "@opentelemetry/resources": "^1.25.1", "@opentelemetry/sdk-trace-base": "^1.25.1", "@opentelemetry/sdk-trace-node": "^1.25.1", "@opentelemetry/semantic-conventions": "^1.25.1", "@turf/turf": "^7.2.0", "adm-zip": "^0.5.14", "admob-rewarded-ads-ssv": "^1.0.1", "app-store-server-api": "^0.7.0", "async": "^3.2.0", "aws-cloudfront-sign": "^2.2.0", "aws-sdk": "^2.606.0", "axios": "^1.7.2", "bcrypt-nodejs": "0.0.3", "blocked": "^1.3.0", "body-parser": "^1.19.0", "bottleneck": "^2.19.5", "bunnycdn-storage": "^1.0.4", "cors": "^2.8.5", "countries-and-timezones": "^3.3.0", "country-code-lookup": "0.0.18", "country-to-currency": "^1.0.6", "countryjs": "^1.8.0", "cron": "^1.8.2", "cross-env": "^7.0.3", "crypto-random-string": "^3.1.0", "csv-parse": "^5.3.0", "csv-stringify": "^6.2.0", "csvtojson": "^2.0.10", "deep-freeze": "0.0.1", "dotenv": "^8.2.0", "email-validator": "^2.0.4", "emoji-regex": "^10.4.0", "express": "^4.17.1", "express-async-handler": "^1.1.4", "express-session": "^1.17.0", "firebase-admin": "^12.7.0", "flatted": "^3.3.1", "geoip-lite": "^1.4.6", "geopoint": "^1.0.1", "googleapis": "^118.0.0", "groq-sdk": "^0.4.0", "haversine": "^1.1.1", "heapdump": "^0.3.15", "horoscope": "^2.0.1", "i18n": "^0.13.3", "in-app-purchase": "^1.11.4", "ioredis": "^5.4.1", "jsonwebtoken": "^8.5.1", "local-reverse-geocoder": "^0.8.0", "locale-codes": "^1.3.1", "lodash": "^4.17.21", "log-timestamp": "^0.3.0", "luxon": "^1.25.0", "mime-types": "^2.1.35", "modern-ahocorasick": "^1.0.1", "moment": "^2.24.0", "moment-timezone": "^0.5.32", "mongodb": "6.5.0", "mongoose": "^8.13.2", "mongoose-geojson-schema": "^2.1.5", "mongoose-sequence": "^5.3.1", "morgan": "^1.9.1", "morgan-body": "^2.4.8", "multer": "^1.4.2", "multer-s3": "^2.9.0", "nanoid": "^3.1.30", "node-cache": "^5.1.2", "nodejs-file-downloader": "^4.13.0", "npm": "^6.13.6", "openai": "^4.6.0", "path": "^0.12.7", "phone": "^3.1.18", "pusher": "^5.1.3", "rate-limiter-flexible": "^5.0.3", "requestretry": "^7.1.0", "semver-compare": "^1.0.0", "sinon": "^9.2.2", "snappy": "^7.2.2", "socket.io": "^2.3.0", "socket.io-redis": "^5.2.0", "stripe": "^11.1.0", "superagent": "^7.1.6", "tiktoken": "^1.0.21", "together-ai": "^0.13.0", "twilio": "^3.77.1", "underscore": "^1.9.2", "unique-names-generator": "^4.7.1", "us-state-codes": "^1.1.2", "uuid": "^9.0.1", "yoti": "4.11.0"}, "devDependencies": {"chai": "^4.2.0", "chai-subset": "^1.6.0", "eslint": "^8.20.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^26.6.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "mocha": "^8.1.3", "mongodb-memory-server": "^8.4.0", "nyc": "^15.1.0", "promise-callbacks": "^3.8.2", "socket.io-client": "^2.3.0", "supertest": "^5.0.0", "temp": "^0.9.4"}, "overrides": {"mongoose": {"mongodb": "6.5.0"}}}